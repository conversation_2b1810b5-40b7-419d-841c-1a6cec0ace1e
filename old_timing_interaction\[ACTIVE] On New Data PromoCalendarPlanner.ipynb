# OLD → NEW
# 'Markets' → 'Market Description'  # Column C
# 'POLE INTERNE' → 'POLE INTERNE'  # Column L (unchanged)
# 'CONDITIONNEMENT' → 'STANDARDIZED PACK TYPE'  # Column M
# 'NBR UNITE' → 'STANDARDIZED NUMBER IN PACK'  # Column O
# 'GAMME' → 'Sub Brand'  # Column I
# 'CTN UNIT' → 'STANDARDIZED PACK SIZE ML'  # Column N
# 'Periods' → 'Period'  # Column P (not 'P End Date')

import pandas as pd
import numpy as np

def a3_load_and_preprocess(a3_path, retailer_filter=None):
   
    # Load A3 promo mapping file
    a3_df = pd.read_excel(a3_path, engine='openpyxl',sheet_name='a3_distribution_march_2025')
    
    # Convert promo start date to datetime
    a3_df['Date de début'] = pd.to_datetime(a3_df['Date de début'], dayfirst=True, errors='coerce')
    
    # Filter by retailer list
    if retailer_filter:
        a3_df = a3_df[a3_df['Enseigne'].isin(retailer_filter)]

    # Filter out everything before April 1, 2022
    a3_df = a3_df[a3_df['Date de début'] >= pd.Timestamp("2022-04-01")]

    # rename similar sku colum 
    a3_df.rename(columns={'Similar SKU Mapping': 'Similar_SKU_Mapping'}, inplace=True)

    print(f"A3 shape after filtering: {a3_df.shape}")
    return a3_df

def niq_load_and_preprocess(niq_path, retailer_filter=None):
    # read the 'RAW' sheet from the Excel file
    niq = pd.read_excel(niq_path, engine='openpyxl', sheet_name='RAW')
    
    # Filter by retailer list
    if retailer_filter:
        niq = niq[niq['Market Description'].isin(retailer_filter)]

    # convert to dates - now using direct date format instead of French text extraction
    niq["date_week_end"] = pd.to_datetime(niq["P End Date"], errors="coerce")
    niq['date_week_start'] = niq['date_week_end'] - pd.DateOffset(days=6)

    # Create clean column names with underscores
    niq["Promo_Value"] = niq["Promo Value TY"].astype(float)
    niq["Non_Promo_Price"] = niq["Non Promo Price TY"].astype(float)
    niq["Promo_Price"] = niq["Promo Price TY"].astype(float)
    niq["Non_Promo_Value"] = niq["Non Promo Value TY"].astype(float)
    niq["Promo_Volume"] = niq["Promo Volume TY"].astype(float)
    niq["Non_Promo_Volume"] = niq["Non Promo Volume TY"].astype(float)
    niq["Base_Volume"] = niq["Base Volume TY"].astype(float)
    niq["Base_Value_PPC"] = niq["Base Value (PPC) TY"].astype(float)
    niq["Promo_Price_PPC"] = niq["Promo Price (PPC) TY"].astype(float)
    niq["Non_Promo_Price_PPC"] = niq["Non Promo Price (PPC) TY"].astype(float)
    
    # Drop the original columns with spaces to avoid confusion
    niq.drop(columns=["Promo Value TY", "Non Promo Price TY", "Promo Price TY", "Non Promo Value TY", 
                      "Promo Volume TY", "Non Promo Volume TY", "Base Volume TY", "Base Value (PPC) TY"], inplace=True)
    
    # Create three datasets:
    # 1. Total Beer per Retailer Dataset (POLE INTERNE is null)
    niq_beer = niq[niq['POLE INTERNE'].isnull()].copy()
    
    # 2. Beer Pole Dataset (POLE INTERNE has values but brand/pack details are null)
    beer_pole = niq[
        (~niq['POLE INTERNE'].isnull()) & 
        (niq['BRAND'].isnull()) & 
        (niq['Sub Brand'].isnull())
    ].copy()
    
    # 3. SKU level data (POLE INTERNE has values and brand/pack details are present)
    niq_sku = niq[
        (~niq['POLE INTERNE'].isnull()) & 
        (~niq['BRAND'].isnull()) & 
        (~niq['Sub Brand'].isnull())
    ].copy()  

    # Process SKU data
    niq_sku['pack_type'] = niq_sku['STANDARDIZED PACK TYPE']
    niq_sku['qty_in_pack'] = niq_sku['STANDARDIZED NUMBER IN PACK'].replace({
        '12x': 'X(12-15)',
        '15x': 'X(12-15)', 
        '20x': 'X(20-24)',
        '24x': 'X(20-24)'
    })
    niq_sku['sku'] = (niq_sku['Sub Brand'] + ' ' + 
                    niq_sku['pack_type'] + ' ' + 
                    niq_sku['qty_in_pack'].str.replace('X', '') + 'X' + 
                    niq_sku['STANDARDIZED PACK SIZE ML'].astype(int).astype(str) + 'ML')

    global sku_segment_mapping, beer_pole_renamed
    
    # Create SKU-to-segment mapping from data with POLE INTERNE
    sku_segment_mapping = niq_sku[['sku', 'POLE INTERNE']].drop_duplicates()
    sku_segment_mapping = sku_segment_mapping.rename(columns={'POLE INTERNE': 'segment'})
    
    # Process beer pole data for segment totals (from original niq, not niq_sku)
    beer_pole_for_segments = niq[~niq['POLE INTERNE'].isnull() & niq['Sub Brand'].isnull()].copy()
    if not beer_pole_for_segments.empty:
        beer_pole_aggregated = beer_pole_for_segments.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({'Volume TY': 'sum', 'Value Sales TY': 'sum'})
        beer_pole_aggregated['Volume TY'] = beer_pole_aggregated['Volume TY'].div(100)
        beer_pole_renamed = beer_pole_aggregated.rename(columns={'POLE INTERNE': 'segment', 'Volume TY': 'segment_volume_hl', 'Value Sales TY': 'segment_value'})
    
    # NOW the groupby aggregation (this removes POLE INTERNE from niq_sku)
    niq_sku = niq_sku.groupby(['Market Description','date_week_start','date_week_end', 'sku'], as_index=False).agg({
        'Volume TY': np.sum, 
        'Value Sales TY': np.sum,
        'Wtd Dist TY': np.max,
        'Num Dist TY': np.max,
        'ROS Value TY': np.max,
        'Promo_Value': np.sum,
        'Non_Promo_Price': np.max,
        'Promo_Price': np.max,
        'Non_Promo_Value': np.sum,
        'Promo_Volume': np.sum,
        'Non_Promo_Volume': np.sum,
        'Base_Volume': np.sum,
        'Base_Value_PPC': np.sum,
        'Non_Promo_Price_PPC': np.max,
        'Promo_Price_PPC': np.max,
    })
    
    # Create derived columns
    niq_sku['volume_hl'] = niq_sku['Volume TY'].div(100)
    niq_sku['promo_volume_hl'] = niq_sku['Promo_Volume'].div(100)
    niq_sku['non_promo_volume_hl'] = niq_sku['Non_Promo_Volume'].div(100)
    niq_sku['base_volume_hl'] = niq_sku['Base_Volume'].div(100)
    niq_sku['value_eur'] = niq_sku['Value Sales TY']
    niq_sku['w_dist'] = niq_sku['Wtd Dist TY']
    niq_sku['num_dist'] = niq_sku['Num Dist TY']
    niq_sku['ros_value'] = niq_sku['ROS Value TY']

    
    niq_sku = niq_sku.drop(columns=['Volume TY', 'Value Sales TY', 'Wtd Dist TY', 'Num Dist TY', 'ROS Value TY'])

    # Aggregate beer data (total beer level)
    niq_beer = niq_beer.groupby(['Market Description','date_week_start','date_week_end', 'TOTAL BEER'], as_index=False).agg({
        'Volume TY': np.sum, 
        'Value Sales TY': np.sum,
        'Promo_Value': np.sum,
        'Non_Promo_Price': np.max,
        'Promo_Price': np.max,
        'Non_Promo_Value': np.sum,
        'Promo_Volume': np.sum,
        'Non_Promo_Volume': np.sum,
        'Base_Volume': np.sum,
        'Base_Value_PPC': np.sum,
        'Non_Promo_Price_PPC': np.max,
        'Promo_Price_PPC': np.max,
    })
    niq_beer.rename(columns={'TOTAL BEER':'sku'}, inplace=True)
    niq_beer['volume_hl'] = niq_beer['Volume TY'].div(100)
    niq_beer['value_eur'] = niq_beer['Value Sales TY']
    niq_beer['promo_volume_hl'] = niq_beer['Promo_Volume'].div(100)
    niq_beer['non_promo_volume_hl'] = niq_beer['Non_Promo_Volume'].div(100)
    niq_beer['base_volume_hl'] = niq_beer['Base_Volume'].div(100)
    niq_beer['base_value_PPC'] = niq_beer['Base_Value_PPC'] 
    niq_beer['non_promo_price_PPC'] = niq_beer['Non_Promo_Price_PPC']
    niq_beer['promo_price_PPC'] = niq_beer['Promo_Price_PPC']
    niq_beer = niq_beer.drop(columns=['Volume TY', 'Value Sales TY', 'Base_Value_PPC', 'Non_Promo_Price_PPC', 'Promo_Price_PPC','Non_Promo_Volume', 'Promo_Volume', 'Base_Volume'])
    
    # Aggregate beer pole data (pole level) - include promo columns in groupby
    beer_pole = beer_pole.groupby(['Market Description','date_week_start','date_week_end', 'POLE INTERNE'], as_index=False).agg({
        'Volume TY': np.sum, 
        'Value Sales TY': np.sum,
        'Promo_Value': np.sum,
        'Non_Promo_Price': np.max,
        'Promo_Price': np.max,
        'Non_Promo_Value': np.sum,
        'Promo_Volume': np.sum,
        'Non_Promo_Volume': np.sum,
        'Base_Volume': np.sum,
        'Base_Value_PPC': np.sum,
        'Non_Promo_Price_PPC': np.max,
        'Promo_Price_PPC': np.max,
        #Before aggreagting, need to calculate weighted average for Promo, then take max of that
    })
    beer_pole.rename(columns={'POLE INTERNE':'sku'}, inplace=True)
    beer_pole['volume_hl'] = beer_pole['Volume TY'].div(100)
    beer_pole['value_eur'] = beer_pole['Value Sales TY']
    beer_pole['promo_volume_hl'] = beer_pole['Promo_Volume'].div(100)
    beer_pole['non_promo_volume_hl'] = beer_pole['Non_Promo_Volume'].div(100)
    beer_pole['base_volume_hl'] = beer_pole['Base_Volume'].div(100)
    beer_pole['base_value_PPC'] = beer_pole['Base_Value_PPC']   
    beer_pole['non_promo_price_PPC'] = beer_pole['Non_Promo_Price_PPC']
    beer_pole['promo_price_PPC'] = beer_pole['Promo_Price_PPC']

    beer_pole = beer_pole.drop(columns=['Volume TY', 'Value Sales TY','Base_Value_PPC', 'Non_Promo_Price_PPC', 'Promo_Price_PPC','Non_Promo_Volume', 'Promo_Volume', 'Base_Volume'])
    
    return niq_sku, niq_beer, beer_pole

# load other data sets
similar_skus = pd.read_csv("Latest_Similar_SKU_Mapping.csv")
weather = pd.read_csv("Weather.csv")
weather['date'] = pd.to_datetime(weather['date']).dt.tz_localize(None)
holidays = pd.read_csv("Holidays.csv")
holidays['date'] = pd.to_datetime(holidays['date']).dt.tz_localize(None)
retailers = pd.read_csv("Retailers.csv")


# # load other data sets
# similar_skus = pd.read_csv("/dbfs/FileStore/RevMan/Latest_Similar_SKU_Mapping.csv")
# weather = pd.read_csv("/dbfs/FileStore/RevMan/Weather.csv")
# weather['date'] = pd.to_datetime(weather['date']).dt.tz_localize(None)
# holidays = pd.read_csv("/dbfs/FileStore/RevMan/Holidays.csv")
# holidays['date'] = pd.to_datetime(holidays['date']).dt.tz_localize(None)
# # retailers = pd.read_csv("Retailers.csv")
# retailers = pd.read_excel('/dbfs/FileStore/RevMan/retailer_mapping.xlsx')

# # read A3 data
# a3_path = "/dbfs/FileStore/RevMan/a3_distribution_march_2025_peter_wf.xlsx"
# selected_retailers = retailers['a3'].to_list()
# a3_df = a3_load_and_preprocess(a3_path, retailer_filter=selected_retailers)

# # read NIQ data
# selected_retailers = retailers['niq'].to_list()
# niq_sku, niq_beer = niq_load_and_preprocess('/dbfs/FileStore/RevMan/NewData.csv', retailer_filter=selected_retailers )

# read A3 data
a3_path = "a3_distribution_march_2025_peter_wf.xlsx"
selected_retailers = retailers['a3'].to_list()
a3_df = a3_load_and_preprocess(a3_path, retailer_filter=selected_retailers)

# read NIQ data
selected_retailers = retailers['niq'].to_list()
niq_sku, niq_beer, beer_pole = niq_load_and_preprocess('latest_niq_extract.xlsx', retailer_filter=selected_retailers)

# Verify mappings were created
print("Segment distribution:")
print(sku_segment_mapping['segment'].value_counts())
print(f"\nBeer pole data: {len(beer_pole_renamed)} records")
print(f"Segments in beer pole: {beer_pole_renamed['segment'].nunique()}")
print("\nFirst few beer pole records:")
print(beer_pole_renamed.head())

display(niq_sku)

display(niq_beer)

display(beer_pole)

# define promo ID
a3_df['promo_id'] = a3_df['Enseigne'] + '_' + a3_df['A3 Distribution SKU'] + '_'+ a3_df['Date de début'].dt.strftime('%Y-%m-%d') + '_'+ a3_df['Date de fin'].dt.strftime('%Y-%m-%d')

print(len(a3_df))
print('total num unique promotions', a3_df['promo_id'].nunique())

temp = a3_df.groupby(['promo_id','Similar_SKU_Mapping' ], as_index=False)['Enseigne'].count().sort_values(by='Enseigne',ascending=False)
# a3_df.loc[a3_df['promo_id'].isin(temp.loc[(temp['Enseigne'] > 1), 'promo_id'].unique())].to_csv('/dbfs/mnt/b2b/RevMan/PromoCalendar/duplicate_promoids.csv')

# define promo granularity
promo = a3_df.groupby(['promo_id', 'Similar_SKU_Mapping', 'Enseigne', 'Date de début', 'Date de fin'], as_index=False, dropna=False).agg({
    'Rounded': 'max',
    'Bucket': 'first',
    'MECHA': 'first',
    'Dn OP': 'max'
})

print('unique number of promotions with attributes', len(promo['promo_id']))

# import pandas as pd
# import numpy as np

# # Ensure date columns are in datetime format
# promo['Date de début'] = pd.to_datetime(promo['Date de début'], errors='coerce')
# promo['Date de fin'] = pd.to_datetime(promo['Date de fin'], errors='coerce')

# # Filter out rows where dates could not be parsed
# promo.dropna(subset=['Date de début', 'Date de fin'], inplace=True)

# abi_promos = promo[promo['Similar_SKU_Mapping'].isin(similar_skus['abi_sku'])]
# comp_promos = promo[promo['Similar_SKU_Mapping'].isin(similar_skus['comp_sku'])]

# records_wo_interaction = []
# records = []

# # Match promos per enseigne
# for retailer in promo['Enseigne'].unique():
#   for abi_sku in similar_skus['abi_sku'].unique():
#     # filter ABI promos only for selected SKU
#     filt_abi_promos = abi_promos[(abi_promos['Enseigne'] == retailer ) & (abi_promos['Similar_SKU_Mapping'] == abi_sku)].copy()
    
#     # skip if no ABI promos
#     if len(filt_abi_promos) == 0:
#       continue

#     for comp_sku in similar_skus.loc[similar_skus['abi_sku'] == abi_sku, 'comp_sku']:
#         # filter Comp promos only for selected SKU
#         filt_comp_promos = comp_promos[(comp_promos['Enseigne'] == retailer ) & (comp_promos['Similar_SKU_Mapping'] == comp_sku)].copy()
        
#         # skip if no Comp promos
#         if len(filt_comp_promos) == 0:
#           continue

#         # iteratively compare each ABI promo vs. each competitor promo 
#         for _, a_row in filt_abi_promos.iterrows():
#           for _, c_row in filt_comp_promos.iterrows(): 
            
#             abi_start, abi_end = a_row['Date de début'], a_row['Date de fin']
#             comp_start, comp_end = c_row['Date de début'], c_row['Date de fin']
            
#             # Fixed origin week calculation (Sunday-based like ISO weeks)
#             origin = pd.Timestamp("2022-01-02")  # Sunday of ISO week 1 in 2022

#             # Calculate week offsets from origin
#             abi_week_val = ((abi_start - origin).days) // 7
#             comp_week_val = ((comp_start - origin).days) // 7

#             # Calculate timing (ABI relative to competitor) 
#             timing_weeks = abi_week_val - comp_week_val
            
#             # Calculate timing (ABI relative to competitor)
#             timing_weeks = abi_week_val - comp_week_val
            
#             # CORRECTED: if promos are more than 4 weeks apart (to capture 3 weeks before/after), treat as no interaction
#             if abs(timing_weeks) > 3:
#               records_wo_interaction.append({
#                 'ABI PromoID': a_row['promo_id'],
#                 'Retailer': retailer,
#                 'ABI SKU': a_row['Similar_SKU_Mapping'],
#                 'ABI Start': abi_start,
#                 'ABI End': abi_end,
#                 'ABI Coverage': a_row['Dn OP'],
#                 'ABI Mechanic': a_row['MECHA'], 
#                 'ABI Depth': a_row['Bucket'],
#                 'ABI Rounded': a_row['Rounded'],
#                 'Competitor PromoID': 'No interaction',
#                 'Competitor SKU': '',
#                 'Competitor Start': np.nan,
#                 'Competitor End': np.nan,
#                 'Competitor Coverage': np.nan,
#                 'Competitor Mechanic': np.nan, 
#                 'Competitor Depth': np.nan,
#                 'Timing': np.nan,
#                 'Weeks since last comp promo': np.nan,
#                 'Weeks until next comp promo': np.nan,
#                 'Overlap Promos': np.nan,
#                 'Actual Overlap Days': np.nan,
#                 'Category': 'No interaction'
#               }) 
#               continue

#             # Calculate overlap (keep original logic)
#             overlap_days = max(0, (min(abi_end, comp_end) - max(abi_start, comp_start)).days)
#             overlap_flag = 1 if overlap_days >= 7 else 0

#             # Map ISO week timing to original weeks_since/weeks_until logic
#             weeks_since = 999
#             weeks_until = -999

#             if overlap_days > 0:
#                 # If overlap, both are 0 (matching original logic)
#                 weeks_since = 0
#                 weeks_until = 0
#             else:
#                 # No overlap - map ISO week timing to original structure
#                 if timing_weeks > 0:
#                     # ABI starts after competitor (positive timing)
#                     weeks_since = timing_weeks  # No need to cap since we already filtered > 4
#                 elif timing_weeks < 0:
#                     # Competitor starts after ABI (negative timing)  
#                     weeks_until = timing_weeks  # No need to cap since we already filtered < -4

#             # Original category logic
#             if overlap_days >= 1:
#                 category = "Overlap"
#             elif weeks_since != 999:
#                 category = "After"
#             elif weeks_until != -999:
#                 category = "Before"
#             else:
#                 category = "Unclear"

#             # KEEP ORIGINAL BEHAVIOR: Don't skip "Unclear" records
#             if category != 'Unclear': 
#               records.append({
#                   'ABI PromoID': a_row['promo_id'],
#                   'Retailer': retailer,
#                   'ABI SKU': a_row['Similar_SKU_Mapping'],
#                   'ABI Start': abi_start,
#                   'ABI End': abi_end,
#                   'ABI Coverage': a_row['Dn OP'],
#                   'ABI Mechanic': a_row['MECHA'], 
#                   'ABI Depth': a_row['Bucket'],
#                   'ABI Rounded': a_row['Rounded'],
#                   'Competitor PromoID': c_row['promo_id'],
#                   'Competitor SKU': c_row['Similar_SKU_Mapping'],
#                   'Competitor Start': comp_start,
#                   'Competitor End': comp_end,
#                   'Competitor Coverage': c_row['Dn OP'],
#                   'Competitor Mechanic': c_row['MECHA'], 
#                   'Competitor Depth': c_row['Bucket'],
#                   'Timing': timing_weeks, 
#                   'Weeks since last comp promo': weeks_since,
#                   'Weeks until next comp promo': weeks_until,
#                   'Overlap Promos': overlap_flag,
#                   'Actual Overlap Days': overlap_days,
#                   'Category': category
#               })

# # Original concatenation logic
# df_w_interaction = pd.DataFrame(records)
# df_wo_interaction = pd.DataFrame(records_wo_interaction).drop_duplicates()
# df_wo_interaction = df_wo_interaction[~df_wo_interaction['ABI PromoID'].isin(df_w_interaction['ABI PromoID'])]

# final_df = pd.concat([df_w_interaction, df_wo_interaction], axis=0)

# print(f"Created {len(final_df)} total records")
# print(f"- Interaction records: {len(df_w_interaction)}")
# print(f"- No-interaction records: {len(df_wo_interaction)}")
# print("\nTiming distribution:")
# print(final_df['Category'].value_counts())
# print("\nISO Week-based timing values:")
# print(final_df['Timing'].value_counts().sort_index())

import pandas as pd
import numpy as np

def calculate_promo_interactions(promo_df, similar_skus_df, max_days_apart=21):
    """
    Calculates the timing interaction between ABI and competitor promotions
    using a direct day-based comparison.
    This function identifies overlapping promotions and promotions that occur
    within a specified number of weeks before or after a competitor's promotion.
    """
    promo_df = promo_df.copy()
    promo_df['Date de début'] = pd.to_datetime(promo_df['Date de début'], errors='coerce')
    promo_df['Date de fin'] = pd.to_datetime(promo_df['Date de fin'], errors='coerce')
    promo_df.dropna(subset=['Date de début', 'Date de fin'], inplace=True)

    abi_promos = promo_df[promo_df['Similar_SKU_Mapping'].isin(similar_skus_df['abi_sku'])].copy()
    comp_promos = promo_df[promo_df['Similar_SKU_Mapping'].isin(similar_skus_df['comp_sku'])].copy()

    common_enseignes = set(abi_promos['Enseigne']) & set(comp_promos['Enseigne'])
    abi_promos = abi_promos[abi_promos['Enseigne'].isin(common_enseignes)]
    comp_promos = comp_promos[comp_promos['Enseigne'].isin(common_enseignes)]

    records = []
    interacting_abi_promo_ids = set()

    for enseigne in common_enseignes:
        abi_sub = abi_promos[abi_promos['Enseigne'] == enseigne]
        comp_sub = comp_promos[comp_promos['Enseigne'] == enseigne]

        for _, a_row in abi_sub.iterrows():
            current_abi_sku = a_row['Similar_SKU_Mapping']
            associated_comp_skus = similar_skus_df[similar_skus_df['abi_sku'] == current_abi_sku]['comp_sku']
            comp_sub_filtered = comp_sub[comp_sub['Similar_SKU_Mapping'].isin(associated_comp_skus)]

            if comp_sub_filtered.empty:
                continue

            for _, c_row in comp_sub_filtered.iterrows():
                if abs((a_row['Date de début'] - c_row['Date de début']).days) > max_days_apart:
                    continue

                abi_start, abi_end = a_row['Date de début'], a_row['Date de fin']
                comp_start, comp_end = c_row['Date de début'], c_row['Date de fin']

                # Default values for 'no interaction' or 'far apart'
                weeks_since = 999
                weeks_until = -999
                timing_val = np.nan
                category = "Unclear"
                
                # First, check for overlap
                overlap_days = max(0, (min(abi_end, comp_end) - max(abi_start, comp_start)).days + 1)
                
                if overlap_days >= 1:
                    category = "Overlap"
                    weeks_since = 0
                    weeks_until = 0
                    timing_val = 0
                    overlap_flag = 1
                else: # No overlap, so check for Before/After
                    overlap_flag = 0
                    days_since = (abi_start - comp_end).days
                    days_until = (comp_start - abi_end).days

                    if 0 < days_since <= max_days_apart:
                        category = "After"
                        timing_val = (days_since + 6) // 7  # Convert days to weeks
                        weeks_since = timing_val
                    elif 0 < days_until <= max_days_apart:
                        category = "Before"
                        timing_val = -((days_until + 6) // 7) # Convert days to weeks and make negative
                        weeks_until = timing_val

                if category != "Unclear":
                    interacting_abi_promo_ids.add(a_row['promo_id'])
                    records.append({
                        'ABI PromoID': a_row['promo_id'], 'Retailer': enseigne,
                        'ABI SKU': a_row['Similar_SKU_Mapping'], 'ABI Start': abi_start, 'ABI End': abi_end,
                        'ABI Coverage': a_row['Dn OP'], 'ABI Mechanic': a_row['MECHA'],
                        'ABI Depth': a_row['Bucket'], 'ABI Rounded': a_row['Rounded'],
                        'Competitor PromoID': c_row['promo_id'], 'Competitor SKU': c_row['Similar_SKU_Mapping'],
                        'Competitor Start': comp_start, 'Competitor End': comp_end,
                        'Competitor Coverage': c_row['Dn OP'], 'Competitor Mechanic': c_row['MECHA'],
                        'Competitor Depth': c_row['Bucket'], 'Timing': timing_val,
                        'Weeks since last comp promo': weeks_since, 'Weeks until next comp promo': weeks_until,
                        'Overlap Promos': overlap_flag, 'Actual Overlap Days': overlap_days, 'Category': category
                    })

    all_abi_promo_ids = set(abi_promos['promo_id'])
    no_interaction_ids = all_abi_promo_ids - interacting_abi_promo_ids
    no_interaction_promos = abi_promos[abi_promos['promo_id'].isin(no_interaction_ids)].drop_duplicates(subset=['promo_id'])
    
    records_wo_interaction = [{
        'ABI PromoID': a_row['promo_id'], 'Retailer': a_row['Enseigne'], 'ABI SKU': a_row['Similar_SKU_Mapping'],
        'ABI Start': a_row['Date de début'], 'ABI End': a_row['Date de fin'], 'ABI Coverage': a_row['Dn OP'],
        'ABI Mechanic': a_row['MECHA'], 'ABI Depth': a_row['Bucket'], 'ABI Rounded': a_row['Rounded'],
        'Competitor PromoID': 'No interaction', 'Competitor SKU': '', 'Competitor Start': pd.NaT, 'Competitor End': pd.NaT,
        'Competitor Coverage': np.nan, 'Competitor Mechanic': np.nan, 'Competitor Depth': np.nan,
        'Timing': np.nan, 'Weeks since last comp promo': 999, 'Weeks until next comp promo': -999,
        'Overlap Promos': 0, 'Actual Overlap Days': 0, 'Category': 'No interaction'
    } for _, a_row in no_interaction_promos.iterrows()]

    df_w_interaction = pd.DataFrame(records).drop_duplicates() if records else pd.DataFrame(records)
    df_wo_interaction = pd.DataFrame(records_wo_interaction)
    final_df = pd.concat([df_w_interaction, df_wo_interaction], ignore_index=True, sort=False)
    
    print(f"Created {len(final_df)} total records")
    print(f"- Interaction records: {len(df_w_interaction)}")
    print(f"- No-interaction records: {len(df_wo_interaction)}")
    print("\nTiming distribution:")
    print(final_df['Category'].value_counts())
    
    return final_df

# Execute the new function to generate the final dataframe
final_df = calculate_promo_interactions(promo, similar_skus, max_days_apart=21)

final_df.head()

# Encode 'Weeks since last comp promo' and 'Weeks until next comp promo' into dummy variables
weeks_since_dummies = pd.get_dummies(final_df['Weeks since last comp promo'], prefix='Wks after')
weeks_until_dummies = pd.get_dummies(final_df['Weeks until next comp promo'], prefix='Wks before')

# Concatenate the dummy variables with the original dataframe
final_df = pd.concat([final_df, weeks_since_dummies, weeks_until_dummies], axis=1)

# CORRECTED: Use integer-based column names
final_df['Overlapping'] = final_df[['Wks before_0', 'Wks after_0', 'Overlap Promos']].max(axis=1) # Any overlap
final_df['Same Week'] = final_df[['Wks after_1','Wks before_-1']].max(axis=1) # Within 7 days but no overlap
final_df['Before'] = final_df[['Wks before_-2', 'Wks before_-3']].max(axis=1) # Group 2 & 3 weeks before as just BEFORE flag
final_df['After'] = final_df[['Wks after_2','Wks after_3']].max(axis=1) # Group 2 & 3 weeks after as just AFTER flag

# CORRECTED: Rename using new integer-based names
final_df = final_df.rename(columns={
    'Wks after_2': '1 wk after',
    'Wks after_3': '2 wk after',       
    'Wks before_-2': '1 wk before',
    'Wks before_-3': '2 wk before'   
})

# CORRECTED: Drop columns using new integer-based names
final_df = final_df.drop(columns=['Wks after_999', 'Wks before_-999', 'Wks after_1','Wks before_-1', 'Wks before_0', 'Wks after_0'])

print("✅ Dummy variables created matching new logic")
print("Available timing columns:")
timing_cols = ['Overlapping', 'Same Week', '1 wk before', '2 wk before', 'Before', '1 wk after', '2 wk after', 'After']

for col in timing_cols:
    if col in final_df.columns:
        count = final_df[col].sum()
        print(f"  {col}: {count} records")


#DEBUG

# --- DEBUG CELL ---
# This cell will help us inspect the dataframe created by the new timing logic
# to understand why the downstream cells are failing.

print("--- DataFrame Info ---")
final_df.info()

print("\n\n--- Category Distribution ---")
print("Value counts for the 'Category' column:")
print(final_df['Category'].value_counts(dropna=False))

print("\n\n--- Timing Column Value Distribution ---")
print("Value counts for 'Weeks since last comp promo':")
print(final_df['Weeks since last comp promo'].value_counts(dropna=False).sort_index())

print("\nValue counts for 'Weeks until next comp promo':")
print(final_df['Weeks until next comp promo'].value_counts(dropna=False).sort_index())


print("\n\n--- Overlap Analysis ---")
print(f"Number of rows with 'Actual Overlap Days' > 0: {len(final_df[final_df['Actual Overlap Days'] > 0])}")

print("\n--- Sample of overlapping rows (if any) ---")
display(final_df[final_df['Actual Overlap Days'] > 0].head())


print("\n\n--- Final DataFrame Columns ---")
print(list(final_df.columns))

print("All timing features already created with clean logic in Previous Cell")
print("Skipping legacy dummy variable encoding")

print(f"\nTiming features summary:")
timing_features = ['Overlapping', 'Same Week', '1 wk before', '2 wk before', 'Before', '1 wk after', '2 wk after', 'After']
# timing_features = ['Overlapping', 'Same Week', '1 wk before', '2 wk before',"3 wk before", 'Before', '1 wk after', '2 wk after',"3 wk after", 'After']
for feature in timing_features:
    count = final_df[feature].sum()
    print(f"{feature}: {count} interactions")

# --- New, Simplified Feature Engineering Cell ---

print("Creating final model features from the new 'Category' and 'Timing' columns...")

# 1. Create the merged 'Same week' column as requested.
# It's 1 if the promotion was an 'Overlap' or started in the 'Same Week Start'.
final_df['Same week'] = ((final_df['Category'] == 'Overlap') | (final_df['Category'] == 'Same Week Start')).astype(int)

# 2. Create the other feature columns directly from the clean 'Timing' data.
final_df['1 wk before'] = (final_df['Timing'] == 1).astype(int)
final_df['2 wk before'] = (final_df['Timing'] == 2).astype(int)
# final_df['3 wk before'] = (final_df['Timing'] == 3).astype(int) 
final_df['Before'] = ((final_df['Timing'] == 1) | (final_df['Timing'] == 2) | (final_df['Timing'] == 3)).astype(int)  

final_df['1 wk after'] = (final_df['Timing'] == -1).astype(int)
final_df['2 wk after'] = (final_df['Timing'] == -2).astype(int)
# final_df['3 wk after'] = (final_df['Timing'] == -3).astype(int) 
final_df['After'] = ((final_df['Timing'] == -1) | (final_df['Timing'] == -2) | (final_df['Timing'] == -3)).astype(int)  

# For any downstream code that might still look for a column named 'Overlapping',
# we can create it as a copy of our new 'Same week' column.
final_df['Overlapping'] = final_df['Same week']


# 3. Display the results to confirm.
# We select the most relevant columns to show the successful creation of the new features.
print("\nSuccessfully created new feature columns.")
print("Displaying the results:")

display_cols = [
    'ABI PromoID', 'Competitor SKU', 'Category', 'Timing', 'Same week',
    'Overlapping', 'Before', 'After', '1 wk before', '2 wk before',
    '1 wk after', '2 wk after'
]

# display_cols = [
#     'ABI PromoID', 'Competitor SKU', 'Category', 'Timing', 'Same week',
#     'Overlapping', 'Before', 'After', '1 wk before', '2 wk before', '3 wk before',  # ADD 3 wk before
#     '1 wk after', '2 wk after', '3 wk after'
# ]
print(final_df[display_cols].head(10))
final_df.shape


final_df

# Ensure all timing flags are present in final_df before aggregation
for col in ['Overlapping', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']:
    if col not in final_df.columns:
        final_df[col] = 0  # Default to 0 if not present


# =============================================================================
# STEP 1: Create Dummy Variables (if not already done)
# =============================================================================

# This check is now unnecessary as Cell 14 handles it, but we'll leave it
# as it is harmless.
timing_cols = ['1 wk before', '2 wk before', '1 wk after', '2 wk after', 'Overlapping']
missing_cols = [col for col in timing_cols if col not in final_df.columns]

if missing_cols:
    print(f"FATAL: Missing expected timing columns: {missing_cols}")
    print("Please re-run Cell 14 to generate the required columns.")
    # Stop execution or raise an error if in a script
    # For a notebook, this print is a clear signal.
else:
    print("✅ Timing columns already exist")

# =============================================================================
# STEP 2: Create Cleaned ADS
# =============================================================================

print("\n🔧 Creating cleaned ADS - removing artificial permutations...")

# FIX: Use np.max instead of the string 'max' to avoid internal pandas error
import numpy as np

ads_df = final_df.groupby(['Retailer', 'ABI PromoID', 'ABI SKU', 'ABI Start', 'ABI End', 'ABI Coverage','ABI Mechanic', 'ABI Depth',"ABI Rounded", 'Competitor SKU'], as_index=False).agg({
  'Competitor Coverage': np.max, 
  'Competitor Mechanic': 'first',
  'Competitor Depth': 'first', 
  'Overlapping': np.max,
  'Same Week': np.max, 
  '1 wk after': np.max,
  '2 wk after': np.max,
  '1 wk before': np.max,
  '2 wk before': np.max,
  'Actual Overlap Days': np.max
})

# Add weather and holiday data
weather['date'] = pd.to_datetime(weather['date'], format='ISO8601')
ads_df['Avg Temp'] = ads_df.apply(
    lambda row: weather[
        (weather['date'] >= row['ABI Start']) & 
        (weather['date'] <= row['ABI End'])
    ]['temp_c'].mean(), axis=1
)

ads_df['KSM'] = ads_df.apply(
    lambda row: 1 if not holidays[
        (holidays['date'] >= row['ABI Start']) & 
        (holidays['date'] <= row['ABI End'])
    ].empty else 0, axis=1
)


print("\n CLEANUP: Nullifying competitor data for zero-interaction records...")

# Define timing columns to check
timing_check_cols = ['1 wk before', '2 wk before', '1 wk after', '2 wk after', 'Overlapping']

# Find records with competitor SKU but all timing flags = 0
# Also check for empty string in Competitor SKU, which denotes 'No Interaction' grouped records
orphaned_mask = (
    (ads_df['Competitor SKU'].notna()) & 
    (ads_df['Competitor SKU'] != '') &
    (ads_df[timing_check_cols].sum(axis=1) == 0)
)

orphaned_count = orphaned_mask.sum()
print(f"Found {orphaned_count} records with competitor SKU but zero timing interactions")

if orphaned_count > 0:
    print("Setting all competitor fields to NULL for these orphaned records...")
    
    # Nuke all competitor fields for orphaned records
    competitor_cols = ['Competitor SKU', 'Competitor Coverage', 'Competitor Mechanic', 'Competitor Depth']
    for col in competitor_cols:
        if col in ads_df.columns:
            if pd.api.types.is_numeric_dtype(ads_df[col]):
                ads_df.loc[orphaned_mask, col] = np.nan
            else:
                ads_df.loc[orphaned_mask, col] = None # Use None for object columns
    
    print(f" Cleaned {orphaned_count} orphaned records - competitor fields set to NULL")
else:
    print("No orphaned records found - data is already clean")

niq_sku.head()

niq_sku.head()

# interacting_promos = final_df[['Retailer','ABI PromoID', 'ABI SKU','ABI Start', 'ABI End',  'Competitor PromoID','Competitor SKU', 'Competitor Start', 'Competitor End']]
# interacting_promos = interacting_promos.merge(retailers, how='left', left_on='Retailer', right_on='a3')

# niq = niq_sku.merge(niq_beer, how='left', on=['Market Description', 'date_week_start','date_week_end'])
# niq = niq.rename(columns={'sku_x':'sku', 'volume_hl_x':'sku_volume_hl', 'value_eur_x':'sku_value', 
#                     'volume_hl_y':'beer_volume_hl', 'value_eur_y':'beer_value'})
# niq = niq.drop(columns='sku_y')

# # Create joined dataframe 
# interacting_promos = interacting_promos.merge(niq, how='left', left_on=['niq', 'ABI SKU'], right_on=['Market Description', 'sku'])

# interacting_promos = interacting_promos.merge(sku_segment_mapping, how='left', left_on=['sku'], right_on=['sku'])

# # Merge segment-level volume and value data
# interacting_promos = interacting_promos.merge(beer_pole_renamed, how='left', 
#                                             left_on=['niq', 'segment', 'date_week_start', 'date_week_end'], 
#                                             right_on=['Market Description', 'segment', 'date_week_start', 'date_week_end'])

# print(f"✅ Added segment data to {len(interacting_promos)} promotion records")

# # CALCULATE ABI PROMO MARKET SHARES DURING PROMO WEEKS
# # keep only overlapping weeks
# promo_ms = interacting_promos[(interacting_promos['ABI Start']<=interacting_promos['date_week_end']) & 
#                    (interacting_promos['ABI End']>=interacting_promos['date_week_start'])]
                   

# # First, merge the ABI Rounded values into promo_ms
# promo_ms = promo_ms.merge(ads_df[['ABI PromoID', 'ABI Rounded']], how='left', on='ABI PromoID')

# # Keep ABI promo to competitor SKU set relationship with mapped NIQ weeks
# comp_set_prorata = promo_ms[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end']].drop_duplicates()

# # Calculate overlapping days per NIQ week
# promo_ms = promo_ms.drop_duplicates(subset=['ABI PromoID', 'date_week_start', 'date_week_end'])
# promo_ms['Overlapping Days'] = promo_ms.apply(
#     lambda row: ((min(row['ABI End'], row['date_week_end']) - max(row['ABI Start'], row['date_week_start']))).days + 1, axis=1)
# promo_ms['niq_pro_rata'] = promo_ms['Overlapping Days'].div(7) # percent of niq week covered by promo

# # ===============================================
# # 🆕 NEW PTC CALCULATION METHOD USING YOUR FORMULA
# # ===============================================

# # 🔥 NEW: Calculate ABI Promo Value using YOUR corrected formula
# # Promo Value = Non Promo Value * (1-Rounded)
# promo_ms['ABI_Promo_Value'] = promo_ms['Non_Promo_Value_x'] * (1 - promo_ms['ABI Rounded'])

# # Convert volumes to HL if needed - use existing HL columns
# promo_ms['ABI_Promo_Volume_HL'] = promo_ms['promo_volume_hl_x']  # Already in HL
# promo_ms['ABI_Non_Promo_Volume_HL'] = promo_ms['non_promo_volume_hl_x']  # Already in HL

# # 🔥 NEW: Calculate Segment Promo Value using same formula
# # For segment data, use proportional calculation based on total segment data
# promo_ms['Segment_Promo_Value'] = promo_ms['segment_value'] * (1 - promo_ms['ABI Rounded'])
# promo_ms['Segment_Promo_Volume_HL'] = promo_ms['segment_volume_hl']  # Already in HL

# comp_set_prorata = comp_set_prorata.merge(promo_ms[['ABI PromoID', 'date_week_end','niq_pro_rata']], how='left', on=['ABI PromoID', 'date_week_end'])
# comp_set_prorata = comp_set_prorata[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end', 'niq_pro_rata']].drop_duplicates()

# abi_sku_promo_niq_weeks = promo_ms[['Retailer','ABI SKU', 'date_week_end']].drop_duplicates()

# # CALCULATE PROMO MS AND PTC FOR ABI SKU
# promo_ms['w_w_dist'] = promo_ms['niq_pro_rata'] * promo_ms['w_dist']
# promo_ms['w_sku_value'] = promo_ms['niq_pro_rata'] * promo_ms['sku_value']
# promo_ms['w_sku_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['sku_volume_hl']
# promo_ms['w_beer_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['beer_volume_hl']
# promo_ms['w_num_dist'] = promo_ms['niq_pro_rata'] * promo_ms['num_dist']

# # ADD: Weighted segment data calculations
# promo_ms['w_segment_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['segment_volume_hl']
# promo_ms['w_segment_value'] = promo_ms['niq_pro_rata'] * promo_ms['segment_value']


# promo_ms['w_ABIPromo_Value'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Value']
# promo_ms['w_ABIPromo_Volume'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Volume_HL']
# promo_ms['w_ABINon_Promo_Value'] = promo_ms['niq_pro_rata'] * promo_ms['Non_Promo_Value_x']
# promo_ms['w_ABIpromo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['ABIpromo_volume_hl_x']
# promo_ms['w_ABInon_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['ABInon_promo_volume_hl']



# promo_ms['w_segment_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Value']
# promo_ms['w_segment_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Volume_HL']

# # Include NEW weighted values in groupby aggregation
# promo_ms = promo_ms.groupby('ABI PromoID', as_index=False).agg({
#     'date_week_end': np.min, 
#     'w_sku_vol_hl': np.sum, 
#     'w_beer_vol_hl': np.sum, 
#     'w_sku_value': np.sum, 
#     'w_w_dist': np.sum, 
#     'w_num_dist': np.sum,
#     'niq_pro_rata': np.sum,
#     'w_segment_volume_hl': np.sum,  
#     'w_segment_value': np.sum,
#     ######
#     'w_ABIPromo_Value': np.sum,
#     'w_ABIPromo_Volume': np.sum,
#     'w_ABINon_Promo_Value': np.sum,
#     'w_ABIpromo_volume_hl': np.sum,
#     'w_ABInon_promo_volume_hl': np.sum,
#     ######
#     'w_ABI_promo_value': np.sum,
#     'w_ABI_promo_volume_hl': np.sum,
#     'w_segment_promo_value': np.sum,
#     'w_segment_promo_volume_hl': np.sum,
#     'ABI Rounded': 'first'
# })

# promo_ms['ABI MS Promo'] = promo_ms['w_sku_vol_hl'].div(promo_ms['w_beer_vol_hl'])*100
# promo_ms['ABI_Promo_W_W_Distribution'] = promo_ms['w_w_dist'].div(promo_ms['niq_pro_rata'])
# promo_ms['ABI_Promo_W_Num_Distribution'] = promo_ms['w_num_dist'].div(promo_ms['niq_pro_rata'])

# # 🔥 NEW: Calculate PTC/HL using YOUR corrected formula
# # ABI Promo PTC/HL = Promo Value / Promo Volume
# promo_ms['ABI Promo PTC/HL'] = promo_ms['w_ABI_promo_value'].div(promo_ms['w_ABI_promo_volume_hl'])

# # 🔥 NEW: Segment PTC/HL using same corrected formula
# promo_ms['Segment Promo PTC/HL'] = promo_ms['w_segment_promo_value'].div(promo_ms['w_segment_promo_volume_hl'])

# # Calculate index
# promo_ms['ABI vs Segment PTC Index'] = promo_ms['ABI Promo PTC/HL'].div(promo_ms['Segment Promo PTC/HL']) * 100

# print("✅ Updated to use YOUR corrected PTC calculation formula!")
# print("✅ NEW: Promo Value = Non Promo Value * (1-Rounded)")
# print("✅ NEW: PTC/HL = Promo Value / Promo Volume")

# comp_set_prorata = comp_set_prorata.merge(niq,how='left', left_on=['niq', 'Competitor SKU', 'date_week_end'], right_on=['Market Description', 'sku','date_week_end'])

# # CALCULATE COMPETITOR SET PTC DURING ABI PROMOTION
# comp_set_prorata['w_sku_value'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_value']
# comp_set_prorata['w_sku_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_volume_hl']
# comp_set_prorata['w_beer_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['beer_volume_hl']

# # ADDED: Calculate weighted distribution for competitor set
# comp_set_prorata['w_w_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['w_dist']
# comp_set_prorata['w_num_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['num_dist']

# # ADDED: Include distribution columns in competitor groupby
# comp_set_prorata = comp_set_prorata.groupby(['ABI PromoID'], as_index=False).agg({
#     'w_sku_vol_hl': np.sum,
#     'w_sku_value': np.sum, 
#     'w_beer_vol_hl': np.max,
#     'w_w_dist': np.sum,
#     'w_num_dist': np.sum,
#     'niq_pro_rata': np.sum
# })

# comp_set_prorata['Comp Promo PTC/HL'] = comp_set_prorata['w_sku_value'].div(comp_set_prorata['w_sku_vol_hl'])

# # ADDED: Calculate competitor distribution metrics
# comp_set_prorata['Comp W_Distribution'] = comp_set_prorata['w_w_dist'].div(comp_set_prorata['niq_pro_rata'])
# comp_set_prorata['Comp Num_Distribution'] = comp_set_prorata['w_num_dist'].div(comp_set_prorata['niq_pro_rata'])

# # CALCULATE ABI BASE MARKET SHARES AS AVG. OF 3 PREV WEEKS (EX. PROMO ON SIMILAR COMPETITION OR ABI SKU)
# # filter out weeks before promotion occurred
# base_ms = interacting_promos.merge(promo_ms[['ABI PromoID', 'date_week_end']], how='left', left_on='ABI PromoID', right_on='ABI PromoID')
# base_ms.rename(columns={'date_week_end_x':'date_week_end', 'date_week_end_y':'promo_week'}, inplace=True)

# base_ms = base_ms[(base_ms['date_week_end'] < base_ms['promo_week'])] 

# # filter out promo weeks of same ABI SKU
# base_ms = base_ms.merge(abi_sku_promo_niq_weeks.rename(columns={'date_week_end':'other_promo_weeks'}), how='left', 
#                         left_on=['Retailer', 'ABI SKU', 'date_week_end'], 
#                         right_on=['Retailer','ABI SKU', 'other_promo_weeks'])

# base_ms = base_ms[base_ms['date_week_end'] != base_ms['other_promo_weeks']]

# # ADDED: Include distribution columns in base_ms selection
# base_ms = base_ms[['Retailer','ABI PromoID', 'ABI SKU', 'ABI Start', 'ABI End', 'date_week_end', 'sku_volume_hl','sku_value', 'beer_volume_hl', 'w_dist', 'num_dist']].drop_duplicates()

# # Select previous 3 date_week_ends per ABI PromoID only
# base_ms = base_ms.sort_values(by=['ABI PromoID', 'date_week_end'], ascending=[True, False])
# base_ms['rank'] = base_ms.groupby('ABI PromoID')['date_week_end'].rank(method='first', ascending=False)
# base_ms = base_ms[base_ms['rank'] <= 3].drop(columns=['rank'])
# base_ms['str_date'] = base_ms['date_week_end'].astype(str)

# # ADDED: Include distribution columns in base_ms groupby
# base_ms = base_ms.groupby(['ABI PromoID'], as_index=False).agg({
#     'sku_volume_hl': np.sum, 
#     'sku_value': np.sum,
#     'beer_volume_hl': np.sum, 
#     'w_dist': np.sum,
#     'num_dist': np.sum,
#     'str_date': ', '.join
# })

# base_ms.rename(columns={'str_date':'base_ms_weeks'}, inplace=True)
# base_ms['ABI MS Base'] = base_ms['sku_volume_hl'].div(base_ms['beer_volume_hl'])*100
# base_ms['ABI Base PTC/HL'] = base_ms['sku_value'].div(base_ms['sku_volume_hl'])

# # ADDED: Calculate base distribution metrics (average over 3 weeks)
# base_ms['ABI Base W_Distribution'] = base_ms['w_dist'].div(3)
# base_ms['ABI Base Num_Distribution'] = base_ms['num_dist'].div(3)

# # Merge promo ms and base ms together
# promo_outcomes = promo_ms.merge(base_ms, how='left', on='ABI PromoID')

# # ADDED: Include competitor distribution columns in merge
# promo_outcomes = promo_outcomes.merge(comp_set_prorata[['ABI PromoID', 'Comp Promo PTC/HL', 'Comp W_Distribution', 'Comp Num_Distribution']], how='left', on='ABI PromoID')

# promo_outcomes['ABI MS Promo Uplift - abs'] = promo_outcomes['ABI MS Promo'] - promo_outcomes['ABI MS Base']
# promo_outcomes['ABI MS Promo Uplift - rel'] = promo_outcomes['ABI MS Promo'].div(promo_outcomes['ABI MS Base'])
# promo_outcomes['ABI Promo PTC/HL Index'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['Comp Promo PTC/HL']) * 100
# promo_outcomes['ABI Promo PTC vs Base'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['ABI Base PTC/HL'])


interacting_promos = final_df[['Retailer','ABI PromoID', 'ABI SKU','ABI Start', 'ABI End',  'Competitor PromoID','Competitor SKU', 'Competitor Start', 'Competitor End']]
interacting_promos = interacting_promos.merge(retailers, how='left', left_on='Retailer', right_on='a3')

niq = niq_sku.merge(niq_beer, how='left', on=['Market Description', 'date_week_start','date_week_end'])
niq = niq.rename(columns={'sku_x':'sku', 'volume_hl_x':'sku_volume_hl', 'value_eur_x':'sku_value', 
                    'volume_hl_y':'beer_volume_hl', 'value_eur_y':'beer_value'})
niq = niq.drop(columns='sku_y')

# Create joined dataframe 
interacting_promos = interacting_promos.merge(niq, how='left', left_on=['niq', 'ABI SKU'], right_on=['Market Description', 'sku'])

interacting_promos = interacting_promos.merge(sku_segment_mapping, how='left', left_on=['sku'], right_on=['sku'])

# Merge segment-level volume and value data
interacting_promos = interacting_promos.merge(beer_pole_renamed, how='left', 
                                            left_on=['niq', 'segment', 'date_week_start', 'date_week_end'], 
                                            right_on=['Market Description', 'segment', 'date_week_start', 'date_week_end'])

print(f"✅ Added segment data to {len(interacting_promos)} promotion records")

# CALCULATE ABI PROMO MARKET SHARES DURING PROMO WEEKS
# keep only overlapping weeks
promo_ms = interacting_promos[(interacting_promos['ABI Start']<=interacting_promos['date_week_end']) & 
                   (interacting_promos['ABI End']>=interacting_promos['date_week_start'])]
                   

# First, merge the ABI Rounded values into promo_ms
promo_ms = promo_ms.merge(ads_df[['ABI PromoID', 'ABI Rounded']], how='left', on='ABI PromoID')

# Keep ABI promo to competitor SKU set relationship with mapped NIQ weeks
comp_set_prorata = promo_ms[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end']].drop_duplicates()

# Calculate overlapping days per NIQ week
promo_ms = promo_ms.drop_duplicates(subset=['ABI PromoID', 'date_week_start', 'date_week_end'])
promo_ms['Overlapping Days'] = promo_ms.apply(
    lambda row: ((min(row['ABI End'], row['date_week_end']) - max(row['ABI Start'], row['date_week_start']))).days + 1, axis=1)
promo_ms['niq_pro_rata'] = promo_ms['Overlapping Days'].div(7) # percent of niq week covered by promo

# ===============================================
# 🆕 NEW PTC CALCULATION METHOD USING YOUR FORMULA
# ===============================================

# 🔥 NEW: Calculate ABI Promo Value using YOUR corrected formula
# Promo Value = Non Promo Value * (1-Rounded)
promo_ms['ABI_Promo_Value'] = promo_ms['Non_Promo_Value_x'] * (1 - promo_ms['ABI Rounded'])

# Add new measure: Calc ABI Promo Value
promo_ms['Calc ABI Promo Value'] = promo_ms['Non_Promo_Value_x'] * (1 - promo_ms['ABI Rounded']) * 10


# Convert volumes to HL if needed - use existing HL columns
promo_ms['ABI_Promo_Volume_HL'] = promo_ms['promo_volume_hl_x']  # Already in HL
promo_ms['ABI_Non_Promo_Volume_HL'] = promo_ms['non_promo_volume_hl_x']  # Already in HL

# 🔥 NEW: Calculate Segment Promo Value using same formula
# For segment data, use proportional calculation based on total segment data
promo_ms['Segment_Promo_Value'] = promo_ms['segment_value'] * (1 - promo_ms['ABI Rounded'])
promo_ms['Segment_Promo_Volume_HL'] = promo_ms['segment_volume_hl']  # Already in HL

comp_set_prorata = comp_set_prorata.merge(promo_ms[['ABI PromoID', 'date_week_end','niq_pro_rata']], how='left', on=['ABI PromoID', 'date_week_end'])
comp_set_prorata = comp_set_prorata[['niq','ABI PromoID', 'Competitor SKU', 'date_week_end', 'niq_pro_rata']].drop_duplicates()

abi_sku_promo_niq_weeks = promo_ms[['Retailer','ABI SKU', 'date_week_end']].drop_duplicates()

# CALCULATE PROMO MS AND PTC FOR ABI SKU
promo_ms['w_w_dist'] = promo_ms['niq_pro_rata'] * promo_ms['w_dist']
promo_ms['w_sku_value'] = promo_ms['niq_pro_rata'] * promo_ms['sku_value']
promo_ms['w_sku_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['sku_volume_hl']
promo_ms['w_beer_vol_hl'] = promo_ms['niq_pro_rata'] * promo_ms['beer_volume_hl']
promo_ms['w_num_dist'] = promo_ms['niq_pro_rata'] * promo_ms['num_dist']

# ADD: Weighted segment data calculations
promo_ms['w_segment_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['segment_volume_hl']
promo_ms['w_segment_value'] = promo_ms['niq_pro_rata'] * promo_ms['segment_value']

# 🆕 NEW: Weighted calculations using your corrected formula
promo_ms['w_ABI_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Value']
promo_ms['w_ABI_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['ABI_Promo_Volume_HL']
promo_ms['w_segment_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Value']
promo_ms['w_segment_promo_volume_hl'] = promo_ms['niq_pro_rata'] * promo_ms['Segment_Promo_Volume_HL']
# Add weighted calculation for new measure
promo_ms['w_Calc_ABI_promo_value'] = promo_ms['niq_pro_rata'] * promo_ms['Calc ABI Promo Value']


# Include NEW weighted values in groupby aggregation
promo_ms = promo_ms.groupby('ABI PromoID', as_index=False).agg({
    'date_week_end': np.min, 
    'w_sku_vol_hl': np.sum, 
    'w_beer_vol_hl': np.sum, 
    'w_sku_value': np.sum, 
    'w_w_dist': np.sum, 
    'w_num_dist': np.sum,
    'niq_pro_rata': np.sum,
    'w_segment_volume_hl': np.sum,  
    'w_segment_value': np.sum,
    # 🆕 NEW: Include your corrected formula values
    'w_ABI_promo_value': np.sum,
    'w_ABI_promo_volume_hl': np.sum,
    'w_segment_promo_value': np.sum,
    'w_segment_promo_volume_hl': np.sum,
    'w_Calc_ABI_promo_value': np.sum, # Add new measure to aggregation
    'ABI Rounded': 'first'
})

promo_ms['ABI MS Promo'] = promo_ms['w_sku_vol_hl'].div(promo_ms['w_beer_vol_hl'])*100
promo_ms['ABI_Promo_W_W_Distribution'] = promo_ms['w_w_dist'].div(promo_ms['niq_pro_rata'])
promo_ms['ABI_Promo_W_Num_Distribution'] = promo_ms['w_num_dist'].div(promo_ms['niq_pro_rata'])

# 🔥 NEW: Calculate PTC/HL using YOUR corrected formula
# ABI Promo PTC/HL = Promo Value / Promo Volume
promo_ms['ABI Promo PTC/HL'] = promo_ms['w_ABI_promo_value'].div(promo_ms['w_ABI_promo_volume_hl'])

# 🔥 NEW: Segment PTC/HL using same corrected formula
promo_ms['Segment Promo PTC/HL'] = promo_ms['w_segment_promo_value'].div(promo_ms['w_segment_promo_volume_hl'])

# Calculate index
promo_ms['ABI vs Segment PTC Index'] = promo_ms['ABI Promo PTC/HL'].div(promo_ms['Segment Promo PTC/HL']) * 100

# Calculate CalcABI Promo PTC
promo_ms['CalcABI Promo PTC'] = promo_ms['w_Calc_ABI_promo_value'].div(promo_ms['w_ABI_promo_volume_hl'])

print("✅ Updated to use YOUR corrected PTC calculation formula!")
print("✅ NEW: Promo Value = Non Promo Value * (1-Rounded)")
print("✅ NEW: PTC/HL = Promo Value / Promo Volume")

comp_set_prorata = comp_set_prorata.merge(niq,how='left', left_on=['niq', 'Competitor SKU', 'date_week_end'], right_on=['Market Description', 'sku','date_week_end'])

# CALCULATE COMPETITOR SET PTC DURING ABI PROMOTION
comp_set_prorata['w_sku_value'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_value']
comp_set_prorata['w_sku_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['sku_volume_hl']
comp_set_prorata['w_beer_vol_hl'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['beer_volume_hl']

# ADDED: Calculate weighted distribution for competitor set
comp_set_prorata['w_w_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['w_dist']
comp_set_prorata['w_num_dist'] = comp_set_prorata['niq_pro_rata'] * comp_set_prorata['num_dist']

# ADDED: Include distribution columns in competitor groupby
comp_set_prorata = comp_set_prorata.groupby(['ABI PromoID'], as_index=False).agg({
    'w_sku_vol_hl': np.sum,
    'w_sku_value': np.sum, 
    'w_beer_vol_hl': np.max,
    'w_w_dist': np.sum,
    'w_num_dist': np.sum,
    'niq_pro_rata': np.sum
})

comp_set_prorata['Comp Promo PTC/HL'] = comp_set_prorata['w_sku_value'].div(comp_set_prorata['w_sku_vol_hl'])

# ADDED: Calculate competitor distribution metrics
comp_set_prorata['Comp W_Distribution'] = comp_set_prorata['w_w_dist'].div(comp_set_prorata['niq_pro_rata'])
comp_set_prorata['Comp Num_Distribution'] = comp_set_prorata['w_num_dist'].div(comp_set_prorata['niq_pro_rata'])

# CALCULATE ABI BASE MARKET SHARES AS AVG. OF 3 PREV WEEKS (EX. PROMO ON SIMILAR COMPETITION OR ABI SKU)
# filter out weeks before promotion occurred
base_ms = interacting_promos.merge(promo_ms[['ABI PromoID', 'date_week_end']], how='left', left_on='ABI PromoID', right_on='ABI PromoID')
base_ms.rename(columns={'date_week_end_x':'date_week_end', 'date_week_end_y':'promo_week'}, inplace=True)

base_ms = base_ms[(base_ms['date_week_end'] < base_ms['promo_week'])] 

# filter out promo weeks of same ABI SKU
base_ms = base_ms.merge(abi_sku_promo_niq_weeks.rename(columns={'date_week_end':'other_promo_weeks'}), how='left', 
                        left_on=['Retailer', 'ABI SKU', 'date_week_end'], 
                        right_on=['Retailer','ABI SKU', 'other_promo_weeks'])

base_ms = base_ms[base_ms['date_week_end'] != base_ms['other_promo_weeks']]

# ADDED: Include distribution columns in base_ms selection
base_ms = base_ms[['Retailer','ABI PromoID', 'ABI SKU', 'ABI Start', 'ABI End', 'date_week_end', 'sku_volume_hl','sku_value', 'beer_volume_hl', 'w_dist', 'num_dist']].drop_duplicates()

# Select previous 3 date_week_ends per ABI PromoID only
base_ms = base_ms.sort_values(by=['ABI PromoID', 'date_week_end'], ascending=[True, False])
base_ms['rank'] = base_ms.groupby('ABI PromoID')['date_week_end'].rank(method='first', ascending=False)
base_ms = base_ms[base_ms['rank'] <= 3].drop(columns=['rank'])
base_ms['str_date'] = base_ms['date_week_end'].astype(str)

# ADDED: Include distribution columns in base_ms groupby
base_ms = base_ms.groupby(['ABI PromoID'], as_index=False).agg({
    'sku_volume_hl': np.sum, 
    'sku_value': np.sum,
    'beer_volume_hl': np.sum, 
    'w_dist': np.sum,
    'num_dist': np.sum,
    'str_date': ', '.join
})

base_ms.rename(columns={'str_date':'base_ms_weeks'}, inplace=True)
base_ms['ABI MS Base'] = base_ms['sku_volume_hl'].div(base_ms['beer_volume_hl'])*100
base_ms['ABI Base PTC/HL'] = base_ms['sku_value'].div(base_ms['sku_volume_hl'])

# ADDED: Calculate base distribution metrics (average over 3 weeks)
base_ms['ABI Base W_Distribution'] = base_ms['w_dist'].div(3)
base_ms['ABI Base Num_Distribution'] = base_ms['num_dist'].div(3)

# Merge promo ms and base ms together
promo_outcomes = promo_ms.merge(base_ms, how='left', on='ABI PromoID')

# ADDED: Include competitor distribution columns in merge
promo_outcomes = promo_outcomes.merge(comp_set_prorata[['ABI PromoID', 'Comp Promo PTC/HL', 'Comp W_Distribution', 'Comp Num_Distribution']], how='left', on='ABI PromoID')

promo_outcomes['ABI MS Promo Uplift - abs'] = promo_outcomes['ABI MS Promo'] - promo_outcomes['ABI MS Base']
promo_outcomes['ABI MS Promo Uplift - rel'] = promo_outcomes['ABI MS Promo'].div(promo_outcomes['ABI MS Base'])
promo_outcomes['ABI Promo PTC/HL Index'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['Comp Promo PTC/HL']) * 100
promo_outcomes['ABI Promo PTC vs Base'] = promo_outcomes['ABI Promo PTC/HL'].div(promo_outcomes['ABI Base PTC/HL'])


# QC for specific promo ID
target_promo = "AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE BLONDE EDITION LIMITEE_12 BTL 25 CL_2022-10-19_2022-10-25"
if target_promo in promo_ms['ABI PromoID'].values:
    result = promo_ms[promo_ms['ABI PromoID'] == target_promo][['ABI PromoID', 'w_Calc_ABI_promo_value', 'w_ABI_promo_volume_hl', 'CalcABI Promo PTC', 'ABI Rounded']]
    print(f"\nResults for promo: {target_promo}")
    print(result)
else:
    print(f"\nPromo ID '{target_promo}' not found in the aggregated promo_ms dataframe.")

# Simple ABI Promo PTC calculation using direct NIQ columns
promo_ms = interacting_promos[(interacting_promos['ABI Start']<=interacting_promos['date_week_end']) & 
                              (interacting_promos['ABI End']>=interacting_promos['date_week_start'])]

# Calculate ABI Promo PTC using direct NIQ columns
promo_ms['ABI Promo PTC'] = promo_ms['Promo_Value_x'] / promo_ms['promo_volume_hl_x']

# Merge with beer_pole data to get segment promo values
promo_ms = promo_ms.merge(beer_pole[['Market Description', 'date_week_start', 'date_week_end', 'sku', 'Promo_Value', 'promo_volume_hl']], 
                         how='left', 
                         left_on=['niq', 'segment', 'date_week_start', 'date_week_end'], 
                         right_on=['Market Description', 'sku', 'date_week_start', 'date_week_end'])

# Calculate Segment Promo PTC using beer_pole promo columns
promo_ms['Segment Promo PTC'] = promo_ms['Promo_Value_y'] / promo_ms['promo_volume_hl_y']

# Aggregate by ABI PromoID for promo-level calculations
promo_ptc_agg = promo_ms.groupby('ABI PromoID', as_index=False).agg({
    'Promo_Value_x': 'sum',
    'promo_volume_hl_x': 'sum', 
    'Promo_Value_y': 'sum',
    'promo_volume_hl_y': 'sum'
})

# Calculate aggregated PTC values
promo_ptc_agg['ABI Promo PTC Agg'] = promo_ptc_agg['Promo_Value_x'] / promo_ptc_agg['promo_volume_hl_x']
promo_ptc_agg['Segment Promo PTC Agg'] = promo_ptc_agg['Promo_Value_y'] / promo_ptc_agg['promo_volume_hl_y']
promo_ptc_agg['ABI vs Segment PTC Index Agg'] = promo_ptc_agg['ABI Promo PTC Agg'] / promo_ptc_agg['Segment Promo PTC Agg']

# Add to promo_outcomes dataframe
promo_outcomes = promo_outcomes.merge(promo_ptc_agg[['ABI PromoID', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg', 'ABI vs Segment PTC Index Agg']], 
                                     how='left', on='ABI PromoID')

# Check result for target promo
target_promo = "AUCHAN SM + SIMPLY MARKET_BUD_(blank)_BIERE BLONDE EDITION LIMITEE_12 BTL 25 CL_2022-10-19_2022-10-25"
result = promo_outcomes[promo_outcomes['ABI PromoID'] == target_promo][['ABI PromoID', 'ABI Promo PTC Agg', 'Segment Promo PTC Agg', 'ABI vs Segment PTC Index Agg']]
print(f"Results for promo: {target_promo}")
print(result)

display(promo_outcomes)
# promo_outcomes = promo_outcomes[promo_outcomes['ABI MS Base'] != 0]


# ads_df = ads_df.merge(promo_outcomes[['ABI PromoID', 'ABI MS Promo', 'ABI MS Base','ABI MS Promo Uplift - abs', 'ABI MS Promo Uplift - rel',  'base_ms_weeks', 
#                                       'ABI Promo PTC/HL Index', 'ABI Promo PTC vs Base', 'ABI Promo PTC/HL','ABI Base PTC/HL', 'Comp Promo PTC/HL','ABI Base W_Distribution','ABI Base Num_Distribution',"Comp W_Distribution","Comp Num_Distribution", "ABI_Promo_W_W_Distribution","ABI_Promo_W_Num_Distribution",
#                                       'Segment Promo PTC/HL', 'ABI vs Segment PTC Index']], how='left', on='ABI PromoID')
ads_df = ads_df.merge(promo_outcomes[['ABI PromoID', 'ABI MS Promo', 'ABI MS Base','ABI MS Promo Uplift - abs', 'ABI MS Promo Uplift - rel',  'base_ms_weeks', 
                                      'ABI Promo PTC/HL Index', 'Comp Promo PTC/HL','ABI Base W_Distribution','ABI Base Num_Distribution',"Comp W_Distribution","Comp Num_Distribution", "ABI_Promo_W_W_Distribution","ABI_Promo_W_Num_Distribution",
                                       "ABI Promo PTC Agg", 'Segment Promo PTC Agg',"ABI vs Segment PTC Index Agg"]], how='left', on='ABI PromoID')
print("Impact analysis before filtering:")
print(f"No interaction records: {sum(ads_df['ABI PromoID'].str.contains('No interaction', na=False))}")
print(f"No interaction with Base=0: {sum((ads_df['ABI PromoID'].str.contains('No interaction', na=False)) & (ads_df['ABI MS Base'] == 0))}")
# Drop rows where 'ABI MS Base' is 0, null, or NaN
print("Before filtering:")
print(f"Total rows: {len(ads_df)}")
print(f"Rows with ABI MS Base = 0: {sum(ads_df['ABI MS Base'] == 0)}")
print(f"Rows with ABI MS Base null/NaN: {sum(ads_df['ABI MS Base'].isna())}")

# Filter out both zero and null values
ads_df = ads_df[(ads_df['ABI MS Base'] != 0) & (ads_df['ABI MS Base'].notna())]

print("\nAfter filtering:")
print(f"Total rows: {len(ads_df)}")
print(f"Remaining ABI MS Base values summary:")
print(ads_df['ABI MS Base'].describe())

#Merging Promo Mechanic
mechanic_mapping = {
    'LV': 'LV',                      # Keep LV as is
    'Multi Buy': 'LV',               # Merge Multi Buy into LV
    'FID': 'FID',                    # Keep FID as is  
    'Loyalty Cards': 'FID',          # Merge Loyalty Cards into FID (if exists)
    'Immediate': 'Immediate',        # Keep Immediate as is
    'RI': 'Immediate',               # Merge RI into Immediate (if exists)
    'No NIP': 'No NIP'               # Keep No NIP as is
}

# Apply the mapping to merge categories
ads_df['ABI Mechanic'] = ads_df['ABI Mechanic'].map(mechanic_mapping).fillna(ads_df['ABI Mechanic'])

print("\nAfter merging categories:")
print(ads_df['ABI Mechanic'].value_counts())

# ads_df = ads_df.drop(columns=['Same Week'])

#ads_df.to_csv('updatedtest_output_ads.csv')


#Merging ABI Promo Depth Volumes

print("Original ABI Depth values:")
print(ads_df['ABI Depth'].value_counts())

# Mapping dictionary for ABI Depth category merging
depth_mapping = {
    '26%-30%': '26%-30%',           # Keep as is
    '21%-25%': '21%-25%',           # Keep as is  
    '<20%': '<20%',                 # Keep as is
    '31%-33%': '31%-33%',           # Keep as is
    0.34: "34%+",               # Merge 0.34 into 34%+
    'Others': '34%+'              # Keep Others as 34%+
}

# Apply the mapping to merge categories
ads_df['ABI Depth'] = ads_df['ABI Depth'].map(depth_mapping).fillna(ads_df['ABI Depth'])



print("\nAfter merging '0.34' and 'Others' categories:")
print(ads_df['ABI Depth'].value_counts())

# Dropping Columns (Comp Start end) used for diagnostics, didnt work anyways
columns_to_drop = [
    'Competitor PromoID',
    "Competitor Start",
    "Competitor End",
    'Timing', 
    'Weeks since last comp promo',
    "Weeks until next comp promo",  
    'Overlap Promos', 
    'Category', 
    'Before', 
    'After'
]

# NOTE: 'Actual Overlap Days' is NOT in the drop list - we want to keep it!
ads_df = ads_df.drop(columns=columns_to_drop, errors='ignore')

# Rename 'Actual Overlap Days' to 'overlapping days' as requested
ads_df = ads_df.rename(columns={'Actual Overlap Days': 'overlapping days'})

list_no_interacting = ads_df.loc[ads_df['Competitor SKU'].isnull(), 'ABI PromoID'].unique()
list_interacting = ads_df.loc[ads_df['Competitor SKU'].notnull(), 'ABI PromoID'].unique()

no_interactions_to_drop = [promo_id for promo_id in list_no_interacting if promo_id in list_interacting]

ads_df = ads_df.loc[~((ads_df['ABI PromoID'].isin(no_interactions_to_drop)) & (ads_df['Competitor SKU'].isnull()))]
# ads_df.to_csv('/dbfs/mnt/b2b/RevMan/PromoCalendar/1new_test_output_ads_v3.csv')

ads_df.to_csv('new_test_output_ads_v3.csv')

# ads_df = pd.read_csv('/dbfs/mnt/b2b/RevMan/PromoCalendar/test_output_ads_v2.csv')

# REMOVING OUTLIERS
def remove_extreme_uplift_outliers(df, outlier_threshold=26.26):

    print("UPLIFT OUTLIER REMOVAL")
    df_work = df.copy()
    
    # basic info for my sanity check
    print(f"Input dataset: {len(df_work)} records")
    print(f"Columns in dataset: {list(df_work.columns)}")

    uplift_column = 'ABI MS Promo Uplift - rel'
    
    retailer_column = 'Retailer'
    original_count = len(df_work)
    
    # Convert uplift to numeric if it's not already
    df_work[uplift_column] = pd.to_numeric(df_work[uplift_column], errors='coerce')
    
    # Identify extreme outliers
    print(f"\n Identifying outliers with {uplift_column} > {outlier_threshold}%...")
    
    extreme_outliers = df_work[df_work[uplift_column] > outlier_threshold].copy()
    
    if len(extreme_outliers) == 0:
        print(f"No outliers found with uplift > {outlier_threshold}%")
        return df_work, pd.DataFrame()
    
    print(f"Found {len(extreme_outliers)} extreme outliers")
    
    # Focus specifically on AUCHAN outliers
    auchan_outliers = extreme_outliers[
        extreme_outliers[retailer_column].str.contains('AUCHAN', case=False, na=False)
    ].copy()
    
    print(f"Found {len(auchan_outliers)} AUCHAN outliers specifically")
    
    # Display details of outliers being removed
    if len(auchan_outliers) > 0:
        print(f"\n--- EXTREME OUTLIER ANALYSIS (Uplift > {outlier_threshold}%) ---")
        print(f"Found {len(auchan_outliers)} outlier promotions for AUCHAN.")
        print("\nDetails of Outlier Promotions:")
        
        relevant_cols = [retailer_column, uplift_column]
        
        # Add other columns if they exist
        optional_cols = [
            'ABI SKU', 'ABI_SKU', 'SKU', 'sku',
            'ABI Start', 'ABI_Start', 'start_date', 'Start_Date',
            'ABI Mechanic', 'ABI_Mechanic', 'mechanic', 'Mechanic',
            'ABI Depth', 'ABI_Depth', 'depth', 'Depth',
            'ABI MS Promo', 'ABI_MS_Promo', 'ms_promo',
            'ABI MS Promo Uplift - abs', 'ABI_MS_Promo_Uplift_abs',
            'Avg Temp', 'Avg_Temp', 'temperature'
        ]
        
        for col in optional_cols:
            if col in df_work.columns:
                relevant_cols.append(col)
        
        # Remove duplicates while preserving order
        relevant_cols = list(dict.fromkeys(relevant_cols))
        
        # Display outlier details
        display_cols = [col for col in relevant_cols if col in auchan_outliers.columns][:8]  # Limit to 8 cols for readability
        
        for idx, (_, row) in enumerate(auchan_outliers.iterrows(), 1):
            print(f"Outlier {idx}:")
            for col in display_cols:
                value = row[col]
                if pd.isna(value):
                    value = "N/A"
                elif isinstance(value, float):
                    if col == uplift_column:
                        value = f"{value:.2f}%"
                    else:
                        value = f"{value:.2f}"
                print(f"  {col}: {value}")
            print()
    
    # Remove the outliers from the dataset
    print(f"Removing {len(auchan_outliers)} AUCHAN outliers from dataset...")
    
    # Create a cleaned dataset by removing the outliers
    cleaned_df = df_work.drop(auchan_outliers.index).copy()
    
    print(f"Cleaned dataset: {len(cleaned_df)} records (removed {len(auchan_outliers)} outliers)")
    
    # Summary statistics
    print(f"\nSUMMARY:")
    print(f"Original dataset: {original_count} records")
    print(f"After removing missing uplift: {len(df_work)} records")
    print(f"Extreme outliers found: {len(extreme_outliers)} records")
    print(f"AUCHAN outliers removed: {len(auchan_outliers)} records")
    print(f"Final cleaned dataset: {len(cleaned_df)} records")
    
    if len(df_work) > 0:
        print(f"\nUplift statistics (before cleaning):")
        print(f"  Mean: {df_work[uplift_column].mean():.2f}%")
        print(f"  Median: {df_work[uplift_column].median():.2f}%")
        print(f"  Max: {df_work[uplift_column].max():.2f}%")
        
        if len(cleaned_df) > 0:
            print(f"\nUplift statistics (after cleaning):")
            print(f"  Mean: {cleaned_df[uplift_column].mean():.2f}%")
            print(f"  Median: {cleaned_df[uplift_column].median():.2f}%")
            print(f"  Max: {cleaned_df[uplift_column].max():.2f}%")
    
    return cleaned_df, auchan_outliers

# Run the outlier removal on the loaded dataset
print("Running outlier removal on the current dataset...")
df_cleaned, outliers_removed = remove_extreme_uplift_outliers(ads_df, outlier_threshold=26.26)

# Update the main dataframe with cleaned data
ads_df = df_cleaned.copy()

print(f"\nOutlier removal complete! Dataset updated with {len(ads_df)} clean records.")


ads_df.to_csv('new_test_output_ads_v3.csv')


# # Final ADS Export
# ads_df.to_csv('/dbfs/FileStore/RevMan/1emock_ads.csv')


# # Load the CSV file into a DataFrame
# ads = spark.read.csv('dbfs:/mnt/b2b/RevMan/PromoCalendar/1mock_test_output_ads_v2.csv', header=True, inferSchema=True)

# # Display the DataFrame
# display(ads)